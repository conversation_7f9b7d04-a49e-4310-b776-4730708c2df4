import { defaultsDeep, keyBy } from 'lodash-es'
import { deviceStatus as deviceStatusDict } from '$/dicts/device/index.js'

/**
 * 获取设备名称
 */
export function getDeviceName(device) {
  return device.product ? device.product.split(':')[1] : '未授权设备'
}

/**
 * 获取备注名称
 */
export function getRemark(deviceId) {
  const value = window.appStore.get('device')?.[deviceId]?.remark
  return value
}

/**
 * 获取历史设备列表
 */
export function getHistoryDevices() {
  const devices = window.appStore.get('device') || {}

  const value = Object.values(devices).map(device => ({
    ...device,
  }))

  return value
}

/**
 * 获取当前连接的设备
 */
export async function getCurrentDevices() {
  const devices = await window.adb.getDeviceList() || []

  return devices.map(device => ({
    ...device,
    id: device.id,
    status: device.type,
    name: getDeviceName(device),
    wifi: ([':', '_adb-tls-connect']).some(item => device.id.includes(item)),
    remark: getRemark(device.id),
  }))
}

/**
 * Scrcpy 配置迁移管理器
 * 用于在设备ID变更时迁移配置，并清理旧配置
 */
export class ScrcpyConfigMigrator {
  constructor() {
    this.processedDeviceIds = new Set()
  }

  /**
   * 从旧设备迁移配置到新设备
   * @param {string} oldDeviceId - 旧设备ID
   * @param {string} newDeviceId - 新设备ID
   * @returns {boolean} 是否成功迁移配置
   */
  migrateConfigFromOldToNew(oldDeviceId, newDeviceId) {
    const scrcpyConfig = this._getScrcpyConfig()

    // 检查是否需要迁移配置
    if (!this._hasConfig(scrcpyConfig, oldDeviceId)) {
      return false
    }

    if (this._hasConfig(scrcpyConfig, newDeviceId)) {
      return false
    }

    // 执行配置迁移
    scrcpyConfig[newDeviceId] = { ...scrcpyConfig[oldDeviceId] }
    this._saveScrcpyConfig(scrcpyConfig)

    // 记录已处理的设备ID，用于后续清理
    this.processedDeviceIds.add(oldDeviceId)

    console.log(`Successfully migrated scrcpy config: ${oldDeviceId} → ${newDeviceId}`)
    return true
  }

  /**
   * 清理已处理的旧设备配置
   */
  cleanupProcessedConfigs() {
    if (this.processedDeviceIds.size === 0) {
      return
    }

    const scrcpyConfig = this._getScrcpyConfig()

    this.processedDeviceIds.forEach((deviceId) => {
      delete scrcpyConfig[deviceId]
    })

    this._saveScrcpyConfig(scrcpyConfig)
    console.log(`Cleaned up ${this.processedDeviceIds.size} old device configs`)
  }

  /**
   * 获取 scrcpy 配置
   * @private
   * @returns {Object} scrcpy 配置对象
   */
  _getScrcpyConfig() {
    return window.appStore.get('scrcpy') || {}
  }

  /**
   * 保存 scrcpy 配置
   * @private
   * @param {Object} config - 配置对象
   */
  _saveScrcpyConfig(config) {
    window.appStore.set('scrcpy', config)
  }

  /**
   * 检查设备是否有配置
   * @private
   * @param {Object} config - 配置对象
   * @param {string} deviceId - 设备ID
   * @returns {boolean} 是否有配置
   */
  _hasConfig(config, deviceId) {
    return Object.keys(config[deviceId] || {}).length > 0
  }
}

export const deviceSortModel = deviceStatusDict.reduce((obj, item, index) => {
  obj[item.value] = index
  return obj
}, {})

/**
 * 检查历史设备是否需要迁移配置
 * @param {Object} historyDevice - 历史设备对象
 * @param {Array} currentDevices - 当前设备列表
 * @returns {boolean} 是否需要迁移
 */
export function shouldMigrateDevice(historyDevice, currentDevices) {
  // 设备ID不匹配（设备已断开或ID发生变化）
  const isDeviceIdMismatch = currentDevices.every(device => !device.id.includes(historyDevice.id))

  // 但序列号匹配（同一设备重新连接）
  const hasMatchingSerialNumber = currentDevices.some(device =>
    device.serialNo.includes(historyDevice.serialNo),
  )

  return isDeviceIdMismatch && hasMatchingSerialNumber
}

/**
 * 查找与历史设备序列号匹配的当前设备
 * @param {Object} historyDevice - 历史设备对象
 * @param {Array} currentDevices - 当前设备列表
 * @returns {Array} 匹配的当前设备列表
 */
export function findMatchingCurrentDevices(historyDevice, currentDevices) {
  return currentDevices.filter(device =>
    device.serialNo.includes(historyDevice.serialNo),
  )
}

/**
 * 合并历史和当前设备列表
 * @param {Array} historyDevices - 历史设备列表
 * @param {Array} currentDevices - 当前设备列表
 * @returns {Array} 合并后的设备列表
 */
export function mergeDevices(historyDevices, currentDevices) {
  // 合并设备列表：当前设备优先，历史设备作为默认值
  const historyDeviceMap = keyBy(historyDevices, 'id')
  const currentDeviceMap = keyBy(currentDevices, 'id')

  const mergedDeviceList = Object.values(defaultsDeep(currentDeviceMap, historyDeviceMap))

  // 按设备状态排序
  return mergedDeviceList.sort((a, b) => deviceSortModel[a.status] - deviceSortModel[b.status])
}

/**
 * 保存设备信息到存储
 */
export function saveDevicesToStore(devices) {
  const cleanedDevices = devices
    .filter(device => !['unauthorized'].includes(device.status))
    .map(device => ({
      ...device,
      status: 'offline',
      type: 'offline',
    }))

  window.appStore.set('device', keyBy(cleanedDevices, 'id'))
}
